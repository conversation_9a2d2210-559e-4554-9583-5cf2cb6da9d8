#!/usr/bin/env python3
"""
Sportradar Odds Comparison Regular API Client

This module provides a comprehensive HTTP client for interacting with the 
Sportradar Odds Comparison Regular API. It includes proper error handling,
JSON response parsing, and methods for various endpoints.

API Documentation: https://developer.sportradar.com/odds/reference/oc-regular-overview
"""

import requests
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, date
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SportradarOCRegularClient:
    """
    A client for the Sportradar Odds Comparison Regular API.
    
    This client provides methods to fetch odds comparison data from various endpoints
    including sports, schedules, markets, and outrights.
    """
    
    def __init__(self, api_key: str, access_level: str = "trial", language: str = "en", odds_format: str = "us"):
        """
        Initialize the Sportradar OC Regular API client.
        
        Args:
            api_key (str): Your Sportradar API key
            access_level (str): API access level (default: "trial")
            language (str): Language code for responses (default: "en")
            odds_format (str): Odds format - "us", "eu", or "uk" (default: "us")
        """
        self.api_key = api_key
        self.access_level = access_level
        self.language = language
        self.odds_format = odds_format
        # The OC Regular API uses a different URL structure
        # Based on the documentation: oddscomparison-{package_type}{access_level}1
        # where package_type is empty for regular API, so it becomes oddscomparison-trial1
        self.base_url = f"https://api.sportradar.com/oddscomparison-{access_level}1/{language}/{odds_format}"
        
        # Configure session for connection pooling and timeout
        self.session = requests.Session()
        self.session.timeout = 30
        
        # Set default headers
        self.session.headers.update({
            'Accept': 'application/json',
            'User-Agent': 'SportradarOCRegularClient/1.0'
        })
    
    def _make_request(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Make a GET request to the Sportradar API with proper error handling.
        
        Args:
            endpoint (str): API endpoint path
            params (dict, optional): Additional query parameters
            
        Returns:
            dict: Parsed JSON response
            
        Raises:
            requests.exceptions.RequestException: For HTTP-related errors
            ValueError: For JSON parsing errors
        """
        # Add API key to parameters
        if params is None:
            params = {}
        params['api_key'] = self.api_key
        
        url = f"{self.base_url}/{endpoint}.json"
        
        try:
            logger.info(f"Making request to: {url}")
            logger.debug(f"Parameters: {params}")
            
            response = self.session.get(url, params=params)
            response.raise_for_status()  # Raise an exception for bad status codes
            
            # Parse JSON response
            try:
                data = response.json()
                logger.info(f"Request successful. Response size: {len(response.content)} bytes")
                return data
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                logger.error(f"Response content: {response.text[:500]}...")
                raise ValueError(f"Invalid JSON response: {e}")
                
        except requests.exceptions.Timeout:
            logger.error("Request timed out")
            raise
        except requests.exceptions.ConnectionError:
            logger.error("Connection error occurred")
            raise
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error occurred: {e}")
            logger.error(f"Response status: {response.status_code}")
            logger.error(f"Response content: {response.text[:500]}...")
            raise
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}")
            raise
    
    def get_sports(self) -> Dict[str, Any]:
        """
        Get a list of available sports and their IDs.
        
        Returns:
            dict: JSON response containing sports data
            
        Example:
            {
                "sports": [
                    {
                        "id": "sr:sport:1",
                        "name": "Soccer"
                    },
                    ...
                ]
            }
        """
        return self._make_request("sports")
    
    def get_books(self) -> Dict[str, Any]:
        """
        Get a list of configured bookmakers and their IDs.
        
        Returns:
            dict: JSON response containing bookmaker data
        """
        return self._make_request("books")
    
    def get_categories(self) -> Dict[str, Any]:
        """
        Get a list of categories where outrights exist.
        
        Returns:
            dict: JSON response containing category data
        """
        return self._make_request("categories")
    
    def get_tournaments(self) -> Dict[str, Any]:
        """
        Get a list of tournaments and their IDs.
        
        Returns:
            dict: JSON response containing tournament data
        """
        return self._make_request("tournaments")
    
    def get_sport_categories(self, sport_id: str) -> Dict[str, Any]:
        """
        Get categories for a sport ID that have outrights available.
        
        Args:
            sport_id (str): Sport ID (e.g., "sr:sport:1" for Soccer)
            
        Returns:
            dict: JSON response containing category data
        """
        endpoint = f"sports/{sport_id}/categories"
        return self._make_request(endpoint)
    
    def get_sport_tournaments(self, sport_id: str) -> Dict[str, Any]:
        """
        Get tournaments for a given sport ID that have schedules available.
        
        Args:
            sport_id (str): Sport ID (e.g., "sr:sport:1" for Soccer)
            
        Returns:
            dict: JSON response containing tournament data
        """
        endpoint = f"sports/{sport_id}/tournaments"
        return self._make_request(endpoint)
    
    def get_daily_sport_schedule(self, sport_id: str, date_str: str, 
                                start: int = 0, limit: int = 50) -> Dict[str, Any]:
        """
        Get a list of sport events for a given sport ID and date that have odds available.
        
        Args:
            sport_id (str): Sport ID (e.g., "sr:sport:1" for Soccer)
            date_str (str): Date in YYYY-MM-DD format
            start (int): Starting index for pagination (default: 0)
            limit (int): Number of results to return (default: 50)
            
        Returns:
            dict: JSON response containing schedule data
        """
        endpoint = f"sports/{sport_id}/schedules/{date_str}/schedule"
        params = {"start": start, "limit": limit}
        return self._make_request(endpoint, params)
    
    def get_tournament_schedule(self, tournament_id: str, 
                               start: int = 0, limit: int = 50) -> Dict[str, Any]:
        """
        Get a list of scheduled sport events by tournament.
        
        Args:
            tournament_id (str): Tournament ID (e.g., "sr:tournament:17")
            start (int): Starting index for pagination (default: 0)
            limit (int): Number of results to return (default: 50)
            
        Returns:
            dict: JSON response containing schedule data
        """
        endpoint = f"tournaments/{tournament_id}/schedule"
        params = {"start": start, "limit": limit}
        return self._make_request(endpoint, params)
    
    def get_sport_event_markets(self, sport_event_id: str) -> Dict[str, Any]:
        """
        Get markets and odds for a given sport event ID where odds exist.
        
        Args:
            sport_event_id (str): Sport event ID (e.g., "sr:sport_event:47395897")
            
        Returns:
            dict: JSON response containing market data with odds
        """
        endpoint = f"sport_events/{sport_event_id}/markets"
        return self._make_request(endpoint)
    
    def get_category_outrights(self, category_id: str) -> Dict[str, Any]:
        """
        Get outrights, competitors and their odds for a given category ID.
        
        Args:
            category_id (str): Category ID (e.g., "sr:category:1")
            
        Returns:
            dict: JSON response containing outright odds data
        """
        endpoint = f"categories/{category_id}/outrights"
        return self._make_request(endpoint)
    
    def get_sport_event_change_log(self, unix_timestamp: int, 
                                  start: int = 0, limit: int = 50) -> Dict[str, Any]:
        """
        Get sport events where odds have changed including changed odds values.
        
        Args:
            unix_timestamp (int): Unix timestamp to check changes from
            start (int): Starting index for pagination (default: 0)
            limit (int): Number of results to return (default: 50)
            
        Returns:
            dict: JSON response containing change log data
        """
        endpoint = f"sport_events/{unix_timestamp}/changes"
        params = {"start": start, "limit": limit}
        return self._make_request(endpoint, params)
    
    # Mapping endpoints
    def get_player_mapping(self, start: int = 0, limit: int = 30000) -> Dict[str, Any]:
        """
        Get player ID mapping between League Specific and General Sport ID types.
        
        Args:
            start (int): Starting index for pagination (default: 0)
            limit (int): Number of results to return (default: 30000, max: 30000)
            
        Returns:
            dict: JSON response containing player mapping data
        """
        endpoint = "players/mapping"
        params = {"start": start, "limit": min(limit, 30000)}
        return self._make_request(endpoint, params)
    
    def get_season_mapping(self, start: int = 0, limit: int = 30000) -> Dict[str, Any]:
        """
        Get season ID mapping between League Specific and General Sport ID types.
        
        Args:
            start (int): Starting index for pagination (default: 0)
            limit (int): Number of results to return (default: 30000, max: 30000)
            
        Returns:
            dict: JSON response containing season mapping data
        """
        endpoint = "seasons/mapping"
        params = {"start": start, "limit": min(limit, 30000)}
        return self._make_request(endpoint, params)
    
    def get_sport_event_mapping(self, start: int = 0, limit: int = 30000) -> Dict[str, Any]:
        """
        Get sport event ID mapping between League Specific and General Sport ID types.
        
        Args:
            start (int): Starting index for pagination (default: 0)
            limit (int): Number of results to return (default: 30000, max: 30000)
            
        Returns:
            dict: JSON response containing sport event mapping data
        """
        endpoint = "sport_events/mapping"
        params = {"start": start, "limit": min(limit, 30000)}
        return self._make_request(endpoint, params)
    
    def get_team_mapping(self, start: int = 0, limit: int = 30000) -> Dict[str, Any]:
        """
        Get team ID mapping between League Specific and General Sport ID types.
        
        Args:
            start (int): Starting index for pagination (default: 0)
            limit (int): Number of results to return (default: 30000, max: 30000)
            
        Returns:
            dict: JSON response containing team mapping data
        """
        endpoint = "teams/mapping"
        params = {"start": start, "limit": min(limit, 30000)}
        return self._make_request(endpoint, params)
    
    def get_tournament_mapping(self, start: int = 0, limit: int = 30000) -> Dict[str, Any]:
        """
        Get tournament ID mapping between League Specific and General Sport ID types.
        
        Args:
            start (int): Starting index for pagination (default: 0)
            limit (int): Number of results to return (default: 30000, max: 30000)
            
        Returns:
            dict: JSON response containing tournament mapping data
        """
        endpoint = "tournaments/mapping"
        params = {"start": start, "limit": min(limit, 30000)}
        return self._make_request(endpoint, params)
    
    def get_venue_mapping(self, start: int = 0, limit: int = 30000) -> Dict[str, Any]:
        """
        Get venue ID mapping between League Specific and General Sport ID types.
        
        Args:
            start (int): Starting index for pagination (default: 0)
            limit (int): Number of results to return (default: 30000, max: 30000)
            
        Returns:
            dict: JSON response containing venue mapping data
        """
        endpoint = "venues/mapping"
        params = {"start": start, "limit": min(limit, 30000)}
        return self._make_request(endpoint, params)
    
    def close(self):
        """Close the HTTP session."""
        self.session.close()
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()


def main():
    """
    Example usage of the SportradarOCRegularClient.
    
    This function demonstrates how to use the client to fetch various types of data
    from the Sportradar OC Regular API.
    """
    # API configuration - Updated with provided OC Regular API key
    API_KEY = "8LifVp06acRkB4vYx6preJS8E8y07s4CTmfdgGHW"
    
    # Create client instance
    with SportradarOCRegularClient(API_KEY) as client:
        try:
            print("=== Sportradar OC Regular API Demo ===\n")
            
            # 1. Get available sports
            print("1. Fetching available sports...")
            sports_data = client.get_sports()
            print(f"Found {len(sports_data.get('sports', []))} sports")
            
            # Display first few sports
            for sport in sports_data.get('sports', [])[:5]:
                print(f"  - {sport.get('name')} (ID: {sport.get('id')})")
            print()
            
            # 2. Get bookmakers
            print("2. Fetching available bookmakers...")
            books_data = client.get_books()
            print(f"Found {len(books_data.get('books', []))} bookmakers")
            
            # Display first few bookmakers
            for book in books_data.get('books', [])[:5]:
                print(f"  - {book.get('name')} (ID: {book.get('id')})")
            print()
            
            # 3. Get categories with outrights
            print("3. Fetching categories with outrights...")
            categories_data = client.get_categories()
            print(f"Found {len(categories_data.get('categories', []))} categories")
            
            # Display first few categories
            for category in categories_data.get('categories', [])[:5]:
                print(f"  - {category.get('name')} (ID: {category.get('id')})")
            print()
            
            # 4. Get daily schedule for Soccer (assuming sr:sport:1)
            print("4. Fetching daily schedule for Soccer...")
            today = date.today().strftime("%Y-%m-%d")
            soccer_id = "sr:sport:1"  # Soccer
            
            try:
                schedule_data = client.get_daily_sport_schedule(soccer_id, today)
                sport_events = schedule_data.get('sport_events', [])
                print(f"Found {len(sport_events)} soccer events for {today}")
                
                # Display first event if available
                if sport_events:
                    event = sport_events[0]
                    print(f"  First event: {event.get('id')}")
                    print(f"  Scheduled: {event.get('scheduled')}")
                    
                    competitors = event.get('competitors', [])
                    if len(competitors) >= 2:
                        print(f"  Teams: {competitors[0].get('name')} vs {competitors[1].get('name')}")
                    
                    # 5. Get markets for this event
                    print(f"\n5. Fetching markets for event {event.get('id')}...")
                    try:
                        markets_data = client.get_sport_event_markets(event.get('id'))
                        markets = markets_data.get('markets', [])
                        print(f"Found {len(markets)} markets")
                        
                        # Display first market
                        if markets:
                            market = markets[0]
                            print(f"  Market: {market.get('name')} (Type ID: {market.get('odds_type_id')})")
                            books = market.get('books', [])
                            print(f"  Available at {len(books)} bookmakers")
                    
                    except Exception as e:
                        print(f"  Error fetching markets: {e}")
                
            except Exception as e:
                print(f"  Error fetching schedules: {e}")
                print("  This might be because there are no events today or the sport ID is incorrect.")
            
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            print(f"Error: {e}")


if __name__ == "__main__":
    main()
