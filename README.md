# Sportradar Player Props API Client

A comprehensive Python HTTP client for the Sportradar Odds Comparison Player Props API. This client provides easy access to player props data with proper error handling, JSON response parsing, and support for all major endpoints.

## Features

- ✅ **Complete API Coverage**: Supports all major Player Props API endpoints
- ✅ **Robust Error Handling**: Comprehensive error handling for HTTP and JSON parsing errors
- ✅ **Authentication**: Built-in API key authentication
- ✅ **Connection Pooling**: Uses session-based requests for better performance
- ✅ **Logging**: Detailed logging for debugging and monitoring
- ✅ **Type Hints**: Full type annotations for better IDE support
- ✅ **Context Manager**: Supports `with` statement for proper resource cleanup

## API Documentation

- **Official Documentation**: https://developer.sportradar.com/odds/reference/oc-player-props-overview
- **OpenAPI Spec**: https://api.sportradar.com/oddscomparison-player-props/trial/v2/openapi/swagger/index.html

## Installation

1. Clone or download the files to your project directory
2. Install the required dependency:

```bash
pip install requests
```

## Quick Start

```python
from sportradar_player_props_client import SportradarPlayerPropsClient

# Your API key
API_KEY = "8LifVp06acRkB4vYx6preJS8E8y07s4CTmfdgGHW"

# Create client and fetch data
with SportradarPlayerPropsClient(API_KEY) as client:
    # Get available sports
    sports = client.get_sports()
    print(f"Available sports: {len(sports.get('sports', []))}")
    
    # Get bookmakers
    books = client.get_books()
    print(f"Available bookmakers: {len(books.get('books', []))}")
    
    # Get daily schedules for Basketball
    from datetime import date
    today = date.today().strftime("%Y-%m-%d")
    schedule = client.get_daily_schedules("sr:sport:2", today)
    
    # Get player props for a specific event
    events = schedule.get('sport_events', [])
    if events:
        event_id = events[0].get('id')
        props = client.get_sport_event_player_props(event_id)
        print(f"Player props available for {len(props.get('player_props', []))} players")
```

## Available Methods

### Core Endpoints

| Method | Description | Parameters |
|--------|-------------|------------|
| `get_sports()` | Get list of available sports | None |
| `get_books()` | Get list of bookmakers | None |
| `get_daily_schedules(sport_id, date, start=0, limit=50)` | Get events for a sport on a specific date | sport_id, date (YYYY-MM-DD) |
| `get_sport_event_player_props(sport_event_id)` | Get player props for a specific event | sport_event_id |
| `get_sport_categories(sport_id)` | Get categories for a sport | sport_id |
| `get_sport_competitions(sport_id)` | Get competitions for a sport | sport_id |

### Common Sport IDs

- Basketball: `sr:sport:2`
- Baseball: `sr:sport:3` 
- Ice Hockey: `sr:sport:4`
- American Football: `sr:sport:1`
- Soccer: `sr:sport:1`

## Example Scripts

### 1. Basic Usage (`sportradar_player_props_client.py`)
The main client with a demonstration of basic functionality.

```bash
python sportradar_player_props_client.py
```

### 2. Advanced Examples (`example_usage.py`)
Advanced usage patterns including odds analysis and data processing.

```bash
python example_usage.py
```

### 3. API Testing (`test_api_endpoints.py`)
Comprehensive testing of all API endpoints.

```bash
python test_api_endpoints.py
```

## API Response Structure

### Sports Response
```json
{
  "sports": [
    {
      "id": "sr:sport:2",
      "name": "Basketball",
      "type": "competition"
    }
  ]
}
```

### Player Props Response
```json
{
  "sport_event": {
    "id": "sr:sport_event:12345",
    "start_time": "2024-01-15T20:00:00+00:00",
    "competitors": [...]
  },
  "player_props": [
    {
      "player": {
        "id": "sr:player:12345",
        "name": "Player Name",
        "competitor_id": "sr:competitor:12345"
      },
      "markets": [
        {
          "id": "sr:market:921",
          "name": "total points (incl. overtime)",
          "books": [
            {
              "id": "sr:book:18186",
              "name": "FanDuel",
              "outcomes": [
                {
                  "total": "25.5",
                  "odds_decimal": "1.91",
                  "odds_american": "-110"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## Available Markets

The API supports various player prop markets including:

### Basketball (NBA)
- Total points, assists, rebounds
- 3-point field goals, steals, blocks
- Double/triple doubles
- Combined stats (points + rebounds, etc.)

### Baseball (MLB)
- Pitcher strikeouts, earned runs
- Total bases, hits, runs, RBIs
- Home runs, stolen bases

### Ice Hockey (NHL)
- Goals, assists, points
- Shots, power play points
- First/last/anytime goalscorer

### American Football (NFL/NCAA)
- Passing yards, completions, touchdowns
- Rushing yards, carries
- Receiving yards, receptions
- Touchdown scorers

## Error Handling

The client includes comprehensive error handling:

```python
try:
    with SportradarPlayerPropsClient(API_KEY) as client:
        data = client.get_sports()
except requests.exceptions.Timeout:
    print("Request timed out")
except requests.exceptions.HTTPError as e:
    print(f"HTTP error: {e}")
except ValueError as e:
    print(f"JSON parsing error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Rate Limits and Caching

- The API has built-in caching with TTL (Time To Live) values
- Sports endpoint: 300s TTL
- Most other endpoints: 60s TTL
- Respect rate limits to avoid being blocked

## Authentication

The API uses API key authentication passed as a query parameter. Your API key is automatically included in all requests.

## Support

For API-related questions, refer to:
- [Sportradar Developer Documentation](https://developer.sportradar.com/odds/reference/oc-player-props-overview)
- [Sportradar Support](https://sportradar.com/contact/)

## License

This client is provided as-is for educational and development purposes. Please ensure you comply with Sportradar's terms of service when using their API.
