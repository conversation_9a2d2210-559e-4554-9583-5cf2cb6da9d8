#!/usr/bin/env python3
"""
Advanced example usage of the Sportradar OC Regular API Client

This script demonstrates various ways to use the SportradarOCRegularClient
to fetch and analyze odds comparison data.
"""

from sportradar_oc_regular_client import SportradarOCRegularClient
from datetime import date, timedelta
import json
from typing import List, Dict, Any
import time


def pretty_print_json(data: Dict[str, Any], title: str = ""):
    """Pretty print JSON data with optional title."""
    if title:
        print(f"\n=== {title} ===")
    print(json.dumps(data, indent=2, default=str))


def find_sport_by_name(sports_data: Dict[str, Any], sport_name: str) -> str:
    """
    Find a sport ID by name.
    
    Args:
        sports_data: Response from get_sports()
        sport_name: Name of the sport to find (case-insensitive)
        
    Returns:
        str: Sport ID if found, empty string otherwise
    """
    for sport in sports_data.get('sports', []):
        if sport.get('name', '').lower() == sport_name.lower():
            return sport.get('id', '')
    return ''


def analyze_market_odds(markets_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze market odds data and return summary statistics.
    
    Args:
        markets_data: Response from get_sport_event_markets()
        
    Returns:
        dict: Analysis summary
    """
    markets = markets_data.get('markets', [])
    
    analysis = {
        'total_markets': len(markets),
        'market_types': {},
        'bookmaker_coverage': {},
        'consensus_data': markets_data.get('consensus', {}),
        'best_odds': {}
    }
    
    for market in markets:
        market_name = market.get('name', 'unknown')
        odds_type_id = market.get('odds_type_id')
        
        # Count market types
        if market_name not in analysis['market_types']:
            analysis['market_types'][market_name] = {
                'count': 0,
                'odds_type_id': odds_type_id,
                'bookmakers': set()
            }
        analysis['market_types'][market_name]['count'] += 1
        
        # Analyze bookmaker coverage
        books = market.get('books', [])
        for book in books:
            book_name = book.get('name', 'unknown')
            if book_name not in analysis['bookmaker_coverage']:
                analysis['bookmaker_coverage'][book_name] = 0
            analysis['bookmaker_coverage'][book_name] += 1
            analysis['market_types'][market_name]['bookmakers'].add(book_name)
        
        # Find best odds for each market
        if market_name not in analysis['best_odds']:
            analysis['best_odds'][market_name] = []
        
        for book in books:
            for outcome in book.get('outcomes', []):
                odds_info = {
                    'bookmaker': book.get('name'),
                    'type': outcome.get('type'),
                    'odds': outcome.get('odds'),
                    'spread': outcome.get('spread'),
                    'total': outcome.get('total'),
                    'trend': outcome.get('odds_trend')
                }
                analysis['best_odds'][market_name].append(odds_info)
    
    # Convert sets to lists for JSON serialization
    for market_type in analysis['market_types']:
        analysis['market_types'][market_type]['bookmakers'] = list(
            analysis['market_types'][market_type]['bookmakers']
        )
    
    return analysis


def find_best_value_bets(markets_data: Dict[str, Any], market_name: str = "2way") -> List[Dict[str, Any]]:
    """
    Find the best value bets for a specific market type.
    
    Args:
        markets_data: Response from get_sport_event_markets()
        market_name: Name of the market to analyze
        
    Returns:
        list: List of best odds for each outcome type
    """
    best_bets = {}
    
    for market in markets_data.get('markets', []):
        if market.get('name') != market_name:
            continue
            
        for book in market.get('books', []):
            for outcome in book.get('outcomes', []):
                outcome_type = outcome.get('type', 'unknown')
                odds = outcome.get('odds')
                
                if not odds:
                    continue
                
                # Convert odds to decimal for comparison (assuming US format)
                try:
                    if odds.startswith('-'):
                        decimal_odds = 1 + (100 / abs(float(odds)))
                    else:
                        decimal_odds = 1 + (float(odds) / 100)
                except (ValueError, AttributeError):
                    continue
                
                if outcome_type not in best_bets or decimal_odds > best_bets[outcome_type]['decimal_odds']:
                    best_bets[outcome_type] = {
                        'bookmaker': book.get('name'),
                        'odds': odds,
                        'decimal_odds': decimal_odds,
                        'spread': outcome.get('spread'),
                        'total': outcome.get('total'),
                        'trend': outcome.get('odds_trend')
                    }
    
    return list(best_bets.values())


def main():
    """Main example function demonstrating various API usage patterns."""
    
    API_KEY = "8LifVp06acRkB4vYx6preJS8E8y07s4CTmfdgGHW"
    
    with SportradarOCRegularClient(API_KEY, odds_format="us") as client:
        print("⚽ Sportradar OC Regular API - Advanced Examples\n")
        
        try:
            # Example 1: Find Soccer games and analyze odds
            print("📊 Example 1: Finding Soccer Games and Analyzing Odds")
            print("-" * 60)
            
            # Get sports and find soccer
            sports_data = client.get_sports()
            soccer_id = find_sport_by_name(sports_data, "Soccer")
            
            if soccer_id:
                print(f"✅ Found Soccer sport ID: {soccer_id}")
                
                # Try today and next few days to find games
                for days_ahead in range(7):
                    check_date = (date.today() + timedelta(days=days_ahead)).strftime("%Y-%m-%d")
                    print(f"\n🗓️  Checking date: {check_date}")
                    
                    try:
                        schedule_data = client.get_daily_sport_schedule(soccer_id, check_date, limit=5)
                        events = schedule_data.get('sport_events', [])
                        
                        if events:
                            print(f"🎯 Found {len(events)} soccer events")
                            
                            # Analyze first event
                            event = events[0]
                            print(f"\n⚽ Analyzing event: {event.get('id')}")
                            print(f"⏰ Scheduled: {event.get('scheduled')}")
                            print(f"📍 Status: {event.get('status')}")
                            
                            competitors = event.get('competitors', [])
                            if len(competitors) >= 2:
                                team1 = competitors[0].get('name', 'Team 1')
                                team2 = competitors[1].get('name', 'Team 2')
                                print(f"🆚 Matchup: {team1} vs {team2}")
                            
                            # Get markets for this event
                            print(f"\n📈 Fetching odds markets...")
                            markets_data = client.get_sport_event_markets(event.get('id'))
                            
                            # Analyze the markets data
                            analysis = analyze_market_odds(markets_data)
                            print(f"📊 Total markets: {analysis['total_markets']}")
                            print(f"🏪 Bookmaker coverage:")
                            
                            # Show top bookmakers by market coverage
                            sorted_books = sorted(analysis['bookmaker_coverage'].items(), 
                                                key=lambda x: x[1], reverse=True)
                            for book, count in sorted_books[:5]:
                                print(f"  • {book}: {count} markets")
                            
                            # Show available market types
                            print(f"\n📋 Available market types:")
                            for market_type, info in analysis['market_types'].items():
                                print(f"  • {market_type} (ID: {info['odds_type_id']}): {len(info['bookmakers'])} bookmakers")
                            
                            # Example 2: Find best value bets
                            print(f"\n💰 Example 2: Best Value Bets Analysis")
                            print("-" * 50)
                            
                            # Analyze 2way market (moneyline)
                            best_2way = find_best_value_bets(markets_data, "2way")
                            if best_2way:
                                print("🎯 Best 2way (Moneyline) odds:")
                                for bet in best_2way:
                                    print(f"  • {bet['bookmaker']}: {bet['odds']} "
                                          f"(Decimal: {bet['decimal_odds']:.3f}) "
                                          f"Trend: {bet.get('trend', 'N/A')}")
                            
                            # Analyze total market
                            best_total = find_best_value_bets(markets_data, "total")
                            if best_total:
                                print("\n🎯 Best Total (Over/Under) odds:")
                                for bet in best_total:
                                    total_line = bet.get('total', 'N/A')
                                    print(f"  • {bet['bookmaker']}: {bet['odds']} "
                                          f"(Total: {total_line}) "
                                          f"Trend: {bet.get('trend', 'N/A')}")
                            
                            # Show consensus data if available
                            consensus = analysis.get('consensus_data')
                            if consensus:
                                print(f"\n📊 Consensus Data Available:")
                                
                                # Bet percentages
                                bet_percentages = consensus.get('bet_percentage_outcomes', {})
                                if bet_percentages:
                                    print("  💹 Bet Percentages:")
                                    for market in bet_percentages.get('markets', []):
                                        market_name = market.get('name')
                                        print(f"    {market_name}:")
                                        for outcome in market.get('outcomes', []):
                                            print(f"      {outcome.get('type')}: {outcome.get('percentage')}%")
                                
                                # Consensus lines
                                lines = consensus.get('lines', {})
                                if lines:
                                    print("  📈 Consensus Lines:")
                                    for line in lines.get('lines', []):
                                        print(f"    {line.get('name')}: {line.get('total', 'N/A')}")
                            
                            break  # Found events, stop looking
                        else:
                            print("❌ No events found")
                    
                    except Exception as e:
                        print(f"⚠️  Error checking {check_date}: {e}")
                        continue
                
            else:
                print("❌ Soccer sport not found")
            
            # Example 3: Category Outrights Analysis
            print(f"\n🏆 Example 3: Category Outrights Analysis")
            print("-" * 50)
            
            categories_data = client.get_categories()
            categories = categories_data.get('categories', [])
            print(f"📊 Total categories with outrights: {len(categories)}")
            
            if categories:
                # Analyze first category
                category = categories[0]
                print(f"\n🎯 Analyzing category: {category.get('name')} ({category.get('id')})")
                
                try:
                    outrights_data = client.get_category_outrights(category.get('id'))
                    outrights = outrights_data.get('outrights', [])
                    print(f"Found {len(outrights)} outrights")
                    
                    if outrights:
                        outright = outrights[0]
                        print(f"Sample outright: {outright.get('name')}")
                        
                        competitors = outright.get('competitors', [])
                        print(f"Competitors: {len(competitors)}")
                        
                        # Show top 3 favorites
                        if competitors:
                            print("Top 3 favorites:")
                            for i, competitor in enumerate(competitors[:3], 1):
                                name = competitor.get('name', 'Unknown')
                                books = competitor.get('books', [])
                                if books:
                                    best_odds = books[0].get('odds', 'N/A')
                                    print(f"  {i}. {name}: {best_odds}")
                
                except Exception as e:
                    print(f"Error fetching outrights: {e}")
            
        except Exception as e:
            print(f"❌ Error occurred: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
