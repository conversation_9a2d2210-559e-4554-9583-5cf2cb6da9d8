#!/usr/bin/env python3
"""
Script para buscar jogos de futebol (soccer) de hoje usando a API Sportradar OC Regular
"""

from sportradar_oc_regular_client import SportradarOCRegularClient
from datetime import date, timedelta
import json

def formatar_data_hora(data_str):
    """Formatar data/hora para exibição em português"""
    if not data_str:
        return "Não informado"
    
    try:
        from datetime import datetime
        dt = datetime.fromisoformat(data_str.replace('Z', '+00:00'))
        return dt.strftime("%d/%m/%Y %H:%M")
    except:
        return data_str

def buscar_jogos_futebol_hoje():
    """Buscar jogos de futebol de hoje"""
    
    API_KEY = "8LifVp06acRkB4vYx6preJS8E8y07s4CTmfdgGHW"
    
    with SportradarOCRegularClient(API_KEY) as client:
        print("⚽ JOGOS DE FUTEBOL HOJE")
        print("=" * 50)
        
        try:
            # Primeiro, vamos buscar os esportes para confirmar o ID do futebol
            print("🔍 Buscando esportes disponíveis...")
            sports_data = client.get_sports()
            
            soccer_id = None
            for sport in sports_data.get('sports', []):
                if sport.get('name', '').lower() == 'soccer':
                    soccer_id = sport.get('id')
                    break
            
            if not soccer_id:
                print("❌ Futebol não encontrado na lista de esportes")
                return
            
            print(f"✅ ID do Futebol: {soccer_id}")
            
            # Buscar jogos de hoje
            hoje = date.today().strftime("%Y-%m-%d")
            print(f"📅 Buscando jogos para: {hoje}")
            
            try:
                # Tentar diferentes formatos de endpoint para schedule
                endpoints_para_testar = [
                    f"sports/{soccer_id}/schedules/{hoje}/schedule",
                    f"sports/{soccer_id}/schedule/{hoje}",
                    f"sports/{soccer_id}/schedules/{hoje}",
                    f"schedule/{soccer_id}/{hoje}"
                ]
                
                schedule_data = None
                endpoint_funcionou = None
                
                for endpoint in endpoints_para_testar:
                    try:
                        print(f"🧪 Testando endpoint: {endpoint}")
                        schedule_data = client._make_request(endpoint, {"start": 0, "limit": 100})
                        endpoint_funcionou = endpoint
                        print(f"✅ Endpoint funcionou: {endpoint}")
                        break
                    except Exception as e:
                        print(f"❌ Endpoint falhou: {endpoint} - {str(e)[:100]}")
                        continue
                
                if not schedule_data:
                    print("❌ Nenhum endpoint de schedule funcionou")
                    
                    # Vamos tentar buscar torneios de futebol como alternativa
                    print("\n🔄 Tentando buscar torneios de futebol...")
                    try:
                        tournaments_data = client.get_sport_tournaments(soccer_id)
                        tournaments = tournaments_data.get('tournaments', [])
                        
                        if tournaments:
                            print(f"🏆 Encontrados {len(tournaments)} torneios de futebol:")
                            for i, tournament in enumerate(tournaments[:10], 1):
                                print(f"  {i}. {tournament.get('name')} (ID: {tournament.get('id')})")
                            
                            # Tentar buscar schedule de um torneio específico
                            primeiro_torneio = tournaments[0]
                            print(f"\n📋 Buscando jogos do torneio: {primeiro_torneio.get('name')}")
                            
                            try:
                                tournament_schedule = client.get_tournament_schedule(
                                    primeiro_torneio.get('id'), limit=20
                                )
                                
                                events = tournament_schedule.get('sport_events', [])
                                if events:
                                    print(f"🎯 Encontrados {len(events)} jogos no torneio:")
                                    
                                    for i, event in enumerate(events[:10], 1):
                                        print(f"\n--- JOGO {i} ---")
                                        print(f"🆔 ID: {event.get('id')}")
                                        print(f"📅 Data: {formatar_data_hora(event.get('scheduled'))}")
                                        print(f"📊 Status: {event.get('status', 'N/A')}")
                                        
                                        competitors = event.get('competitors', [])
                                        if len(competitors) >= 2:
                                            team1 = competitors[0].get('name', 'Time 1')
                                            team2 = competitors[1].get('name', 'Time 2')
                                            print(f"🆚 {team1} vs {team2}")
                                        
                                        # Tentar buscar odds para este jogo
                                        try:
                                            print("💰 Buscando odds...")
                                            markets_data = client.get_sport_event_markets(event.get('id'))
                                            markets = markets_data.get('markets', [])
                                            
                                            if markets:
                                                print(f"📊 {len(markets)} mercados disponíveis:")
                                                for market in markets[:3]:  # Mostrar apenas os 3 primeiros
                                                    market_name = market.get('name', 'N/A')
                                                    books = market.get('books', [])
                                                    print(f"  • {market_name}: {len(books)} casas de apostas")
                                            else:
                                                print("❌ Nenhuma odd disponível")
                                        except Exception as e:
                                            print(f"❌ Erro ao buscar odds: {str(e)[:50]}...")
                                
                                else:
                                    print("❌ Nenhum jogo encontrado no torneio")
                                    
                            except Exception as e:
                                print(f"❌ Erro ao buscar schedule do torneio: {e}")
                        
                        else:
                            print("❌ Nenhum torneio de futebol encontrado")
                            
                    except Exception as e:
                        print(f"❌ Erro ao buscar torneios: {e}")
                    
                    return
                
                # Se chegou aqui, o schedule funcionou
                events = schedule_data.get('sport_events', [])
                
                if not events:
                    print(f"❌ Nenhum jogo de futebol encontrado para hoje ({hoje})")
                    
                    # Tentar próximos dias
                    print("\n🔍 Buscando jogos nos próximos dias...")
                    for dias_a_frente in range(1, 8):
                        data_futura = (date.today() + timedelta(days=dias_a_frente)).strftime("%Y-%m-%d")
                        try:
                            future_schedule = client._make_request(
                                endpoint_funcionou.replace(hoje, data_futura), 
                                {"start": 0, "limit": 10}
                            )
                            future_events = future_schedule.get('sport_events', [])
                            
                            if future_events:
                                print(f"✅ {len(future_events)} jogos encontrados em {data_futura}")
                                break
                        except:
                            continue
                    
                    return
                
                print(f"🎯 Encontrados {len(events)} jogos de futebol para hoje!")
                print("\n" + "="*60)
                
                for i, event in enumerate(events, 1):
                    print(f"\n🏆 JOGO {i}")
                    print("-" * 30)
                    print(f"🆔 ID: {event.get('id')}")
                    print(f"📅 Data/Hora: {formatar_data_hora(event.get('scheduled'))}")
                    print(f"📊 Status: {event.get('status', 'N/A')}")
                    
                    # Informações do torneio
                    tournament = event.get('tournament', {})
                    if tournament:
                        print(f"🏆 Torneio: {tournament.get('name', 'N/A')}")
                        category = tournament.get('category', {})
                        if category:
                            print(f"🌍 País/Liga: {category.get('name', 'N/A')}")
                    
                    # Times
                    competitors = event.get('competitors', [])
                    if len(competitors) >= 2:
                        team1 = competitors[0]
                        team2 = competitors[1]
                        print(f"🏠 Casa: {team1.get('name', 'Time 1')} ({team1.get('qualifier', 'N/A')})")
                        print(f"✈️  Visitante: {team2.get('name', 'Time 2')} ({team2.get('qualifier', 'N/A')})")
                    
                    # Venue
                    venue = event.get('venue', {})
                    if venue:
                        print(f"🏟️  Estádio: {venue.get('name', 'N/A')}")
                        if venue.get('city_name'):
                            print(f"🌆 Cidade: {venue.get('city_name')}")
                    
                    # Buscar odds se disponível
                    try:
                        print("💰 Buscando odds...")
                        markets_data = client.get_sport_event_markets(event.get('id'))
                        markets = markets_data.get('markets', [])
                        
                        if markets:
                            print(f"📊 {len(markets)} mercados de apostas disponíveis")
                            
                            # Mostrar mercado principal (2way/moneyline)
                            for market in markets:
                                if market.get('name') == '2way':
                                    print("💵 Odds Principais (Vitória):")
                                    books = market.get('books', [])
                                    for book in books[:3]:  # Mostrar apenas 3 casas
                                        book_name = book.get('name', 'N/A')
                                        outcomes = book.get('outcomes', [])
                                        if len(outcomes) >= 2:
                                            home_odds = outcomes[0].get('odds', 'N/A')
                                            away_odds = outcomes[1].get('odds', 'N/A')
                                            print(f"  📱 {book_name}: Casa {home_odds} | Visitante {away_odds}")
                                    break
                        else:
                            print("❌ Nenhuma odd disponível para este jogo")
                            
                    except Exception as e:
                        print(f"❌ Erro ao buscar odds: {str(e)[:50]}...")
                    
                    if i >= 10:  # Limitar a 10 jogos para não sobrecarregar
                        print(f"\n... e mais {len(events) - 10} jogos")
                        break
                
            except Exception as e:
                print(f"❌ Erro ao buscar schedule: {e}")
                
        except Exception as e:
            print(f"❌ Erro geral: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    buscar_jogos_futebol_hoje()
