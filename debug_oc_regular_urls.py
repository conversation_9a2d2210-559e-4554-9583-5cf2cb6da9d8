#!/usr/bin/env python3
"""
Debug script to test different URL patterns for the OC Regular API
"""

import requests
import json

API_KEY = "8LifVp06acRkB4vYx6preJS8E8y07s4CTmfdgGHW"

# Different URL patterns to try based on documentation analysis
url_patterns = [
    # Pattern 1: Current attempt
    "https://api.sportradar.com/oddscomparison-trial1/en/us/sports.json",
    
    # Pattern 2: Without version number
    "https://api.sportradar.com/oddscomparison-trial/en/us/sports.json",
    
    # Pattern 3: With v1 explicitly
    "https://api.sportradar.com/oddscomparison-trial/v1/en/us/sports.json",
    
    # Pattern 4: Similar to Player Props but for regular
    "https://api.sportradar.com/oddscomparison-regular/trial/v1/en/sports.json",
    
    # Pattern 5: Without odds format
    "https://api.sportradar.com/oddscomparison-trial1/en/sports.json",
    
    # Pattern 6: Different access level format
    "https://api.sportradar.com/oddscomparison/trial/v1/en/us/sports.json",
    
    # Pattern 7: Just like prematch but regular
    "https://api.sportradar.com/oddscomparison-prematch/trial/v1/en/us/sports.json",
    
    # Pattern 8: Try without language/odds format
    "https://api.sportradar.com/oddscomparison-trial1/sports.json",
    
    # Pattern 9: Try with different version
    "https://api.sportradar.com/oddscomparison-trial/v2/en/us/sports.json",
    
    # Pattern 10: Try the exact pattern from Player Props but with different product
    "https://api.sportradar.com/oddscomparison/trial/v1/en/sports.json"
]

def test_url_pattern(url, api_key):
    """Test a specific URL pattern"""
    try:
        print(f"\n🧪 Testing: {url}")
        
        response = requests.get(url, params={'api_key': api_key}, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ SUCCESS! Response size: {len(response.content)} bytes")
                if 'sports' in data:
                    print(f"Found {len(data['sports'])} sports")
                    for sport in data['sports'][:3]:
                        print(f"  - {sport.get('name')} ({sport.get('id')})")
                return True, data
            except json.JSONDecodeError:
                print(f"✅ SUCCESS but invalid JSON response")
                print(f"Response content: {response.text[:200]}...")
                return True, response.text
        
        elif response.status_code == 403:
            print(f"❌ 403 Forbidden - API key doesn't have access")
        elif response.status_code == 404:
            print(f"❌ 404 Not Found - URL pattern incorrect")
        elif response.status_code == 401:
            print(f"❌ 401 Unauthorized - Authentication issue")
        else:
            print(f"❌ {response.status_code} - {response.reason}")
            print(f"Response: {response.text[:200]}...")
        
        return False, None
        
    except requests.exceptions.Timeout:
        print(f"❌ Timeout")
        return False, None
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error")
        return False, None
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, None

def main():
    print("🔍 Testing different URL patterns for OC Regular API")
    print("=" * 60)
    print(f"API Key: {API_KEY}")
    
    successful_patterns = []
    
    for i, url in enumerate(url_patterns, 1):
        print(f"\n[{i}/{len(url_patterns)}]", end="")
        success, data = test_url_pattern(url, API_KEY)
        
        if success:
            successful_patterns.append((url, data))
    
    print(f"\n" + "=" * 60)
    print(f"📊 SUMMARY")
    print(f"=" * 60)
    
    if successful_patterns:
        print(f"✅ Found {len(successful_patterns)} working URL pattern(s):")
        for url, data in successful_patterns:
            print(f"  • {url}")
    else:
        print(f"❌ No working URL patterns found")
        print(f"\nPossible reasons:")
        print(f"  1. API key doesn't have access to OC Regular API")
        print(f"  2. OC Regular API requires different authentication")
        print(f"  3. API endpoint structure is different than documented")
        print(f"  4. API key is only valid for Player Props API")
        
        print(f"\n💡 Recommendations:")
        print(f"  1. Verify API key has OC Regular API access")
        print(f"  2. Check if separate API key needed for OC Regular")
        print(f"  3. Contact Sportradar support for correct URL format")
        print(f"  4. Try using the working Player Props API instead")

if __name__ == "__main__":
    main()
