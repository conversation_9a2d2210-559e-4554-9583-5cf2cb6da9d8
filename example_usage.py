#!/usr/bin/env python3
"""
Example usage of the Sportradar Player Props API Client

This script demonstrates various ways to use the SportradarPlayerPropsClient
to fetch and analyze player props data.
"""

from sportradar_player_props_client import SportradarPlayerPropsClient
from datetime import date, timedelta
import json
from typing import List, Dict, Any


def pretty_print_json(data: Dict[str, Any], title: str = ""):
    """Pretty print JSON data with optional title."""
    if title:
        print(f"\n=== {title} ===")
    print(json.dumps(data, indent=2, default=str))


def find_sport_by_name(sports_data: Dict[str, Any], sport_name: str) -> str:
    """
    Find a sport ID by name.
    
    Args:
        sports_data: Response from get_sports()
        sport_name: Name of the sport to find (case-insensitive)
        
    Returns:
        str: Sport ID if found, empty string otherwise
    """
    for sport in sports_data.get('sports', []):
        if sport.get('name', '').lower() == sport_name.lower():
            return sport.get('id', '')
    return ''


def analyze_player_props(props_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze player props data and return summary statistics.
    
    Args:
        props_data: Response from get_sport_event_player_props()
        
    Returns:
        dict: Analysis summary
    """
    players = props_data.get('player_props', [])
    
    analysis = {
        'total_players': len(players),
        'total_markets': 0,
        'total_books': 0,
        'market_types': set(),
        'book_names': set(),
        'players_with_props': []
    }
    
    for player in players:
        player_info = player.get('player', {})
        markets = player.get('markets', [])
        
        player_summary = {
            'name': player_info.get('name'),
            'id': player_info.get('id'),
            'team_id': player_info.get('competitor_id'),
            'market_count': len(markets)
        }
        
        for market in markets:
            analysis['total_markets'] += 1
            analysis['market_types'].add(market.get('name'))
            
            for book in market.get('books', []):
                analysis['total_books'] += 1
                analysis['book_names'].add(book.get('name'))
        
        analysis['players_with_props'].append(player_summary)
    
    # Convert sets to lists for JSON serialization
    analysis['market_types'] = list(analysis['market_types'])
    analysis['book_names'] = list(analysis['book_names'])
    
    return analysis


def find_best_odds(props_data: Dict[str, Any], player_name: str, market_name: str) -> List[Dict[str, Any]]:
    """
    Find the best odds for a specific player and market across all bookmakers.
    
    Args:
        props_data: Response from get_sport_event_player_props()
        player_name: Name of the player
        market_name: Name of the market (e.g., "total points (incl. overtime)")
        
    Returns:
        list: List of outcomes with odds from different bookmakers
    """
    best_odds = []
    
    for player in props_data.get('player_props', []):
        player_info = player.get('player', {})
        
        if player_info.get('name', '').lower() != player_name.lower():
            continue
            
        for market in player.get('markets', []):
            if market.get('name', '').lower() != market_name.lower():
                continue
                
            for book in market.get('books', []):
                for outcome in book.get('outcomes', []):
                    odds_info = {
                        'bookmaker': book.get('name'),
                        'player': player_info.get('name'),
                        'market': market.get('name'),
                        'total': outcome.get('total'),
                        'odds_decimal': outcome.get('odds_decimal'),
                        'odds_american': outcome.get('odds_american'),
                        'trend': outcome.get('trend')
                    }
                    best_odds.append(odds_info)
    
    # Sort by decimal odds (higher is better for over bets)
    best_odds.sort(key=lambda x: float(x.get('odds_decimal', 0)), reverse=True)
    return best_odds


def main():
    """Main example function demonstrating various API usage patterns."""
    
    API_KEY = "8LifVp06acRkB4vYx6preJS8E8y07s4CTmfdgGHW"
    
    with SportradarPlayerPropsClient(API_KEY) as client:
        print("🏀 Sportradar Player Props API - Advanced Examples\n")
        
        try:
            # Example 1: Find NBA games and analyze player props
            print("📊 Example 1: Finding NBA Basketball Games")
            print("-" * 50)
            
            # Get sports and find basketball
            sports_data = client.get_sports()
            basketball_id = find_sport_by_name(sports_data, "Basketball")
            
            if basketball_id:
                print(f"✅ Found Basketball sport ID: {basketball_id}")
                
                # Try today and next few days to find games
                for days_ahead in range(7):
                    check_date = (date.today() + timedelta(days=days_ahead)).strftime("%Y-%m-%d")
                    print(f"\n🗓️  Checking date: {check_date}")
                    
                    try:
                        schedule_data = client.get_daily_schedules(basketball_id, check_date, limit=5)
                        events = schedule_data.get('sport_events', [])
                        
                        if events:
                            print(f"🎯 Found {len(events)} basketball events")
                            
                            # Analyze first event
                            event = events[0]
                            print(f"\n🏆 Analyzing event: {event.get('id')}")
                            print(f"⏰ Start time: {event.get('start_time')}")
                            
                            competitors = event.get('competitors', [])
                            if len(competitors) >= 2:
                                team1 = competitors[0].get('name', 'Team 1')
                                team2 = competitors[1].get('name', 'Team 2')
                                print(f"🆚 Matchup: {team1} vs {team2}")
                            
                            # Get player props for this event
                            print(f"\n📈 Fetching player props...")
                            props_data = client.get_sport_event_player_props(event.get('id'))
                            
                            # Analyze the props data
                            analysis = analyze_player_props(props_data)
                            print(f"👥 Players with props: {analysis['total_players']}")
                            print(f"📊 Total markets: {analysis['total_markets']}")
                            print(f"🏪 Unique bookmakers: {len(analysis['book_names'])}")
                            
                            # Show available market types
                            print(f"\n📋 Available market types:")
                            for market_type in analysis['market_types'][:10]:  # Show first 10
                                print(f"  • {market_type}")
                            
                            # Show top players by number of markets
                            print(f"\n🌟 Top players by market availability:")
                            sorted_players = sorted(analysis['players_with_props'], 
                                                  key=lambda x: x['market_count'], reverse=True)
                            
                            for player in sorted_players[:5]:  # Show top 5
                                print(f"  • {player['name']}: {player['market_count']} markets")
                            
                            # Example 2: Find best odds for a specific player/market
                            if sorted_players:
                                top_player = sorted_players[0]
                                print(f"\n💰 Example 2: Best odds analysis for {top_player['name']}")
                                print("-" * 50)
                                
                                # Try to find points market for this player
                                best_odds = find_best_odds(props_data, top_player['name'], "total points")
                                
                                if best_odds:
                                    print(f"🎯 Found {len(best_odds)} odds for points market:")
                                    for i, odds in enumerate(best_odds[:5], 1):  # Show top 5
                                        print(f"  {i}. {odds['bookmaker']}: "
                                              f"{odds['odds_decimal']} ({odds['odds_american']}) "
                                              f"Total: {odds['total']}")
                                else:
                                    print("❌ No points market found for this player")
                            
                            break  # Found events, stop looking
                        else:
                            print("❌ No events found")
                    
                    except Exception as e:
                        print(f"⚠️  Error checking {check_date}: {e}")
                        continue
                
            else:
                print("❌ Basketball sport not found")
            
            # Example 3: Bookmaker analysis
            print(f"\n📚 Example 3: Bookmaker Analysis")
            print("-" * 50)
            
            books_data = client.get_books()
            bookmakers = books_data.get('books', [])
            print(f"📊 Total bookmakers available: {len(bookmakers)}")
            
            print("\n🏪 Available bookmakers:")
            for book in bookmakers[:10]:  # Show first 10
                print(f"  • {book.get('name')} (ID: {book.get('id')})")
            
        except Exception as e:
            print(f"❌ Error occurred: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
